"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  CheckCircle,
  XCircle,
  Loader2,
  Mail,
  ArrowLeft
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";

function VerifyEmailContent() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [verifying, setVerifying] = useState(false);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uid = searchParams.get("uid");

  useEffect(() => {
    if (!uid) {
      setError("Invalid verification link");
      return;
    }

    if (!user) {
      // Wait for user to load
      return;
    }

    // Auto-verify email when page loads
    verifyEmail();
  }, [uid, user]);

  const verifyEmail = async () => {
    if (!user || !uid) return;

    setVerifying(true);
    setError(null);

    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/verify-email`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ uid }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to verify email");
      }

      setVerified(true);
      toast.success("Email verified successfully!");
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 3000);

    } catch (error) {
      console.error("Error verifying email:", error);
      setError(error instanceof Error ? error.message : "Failed to verify email");
      toast.error("Failed to verify email");
    } finally {
      setVerifying(false);
    }
  };

  const goToDashboard = () => {
    router.push("/dashboard");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {verifying ? (
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                  <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                </div>
              ) : verified ? (
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              ) : error ? (
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
              ) : (
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                  <Mail className="h-8 w-8 text-blue-600" />
                </div>
              )}
            </div>
            <CardTitle className="text-2xl font-bold">
              {verifying ? "Verifying Email..." : verified ? "Email Verified!" : error ? "Verification Failed" : "Email Verification"}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            {verifying && (
              <p className="text-muted-foreground">
                Please wait while we verify your email address...
              </p>
            )}

            {verified && (
              <>
                <p className="text-muted-foreground">
                  Your email address has been successfully verified! You can now access all features of your AdMesh account.
                </p>
                <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Email Verified
                </Badge>
                <p className="text-sm text-muted-foreground">
                  Redirecting to dashboard in 3 seconds...
                </p>
              </>
            )}

            {error && (
              <>
                <p className="text-muted-foreground">
                  {error}
                </p>
                <p className="text-sm text-muted-foreground">
                  Please try requesting a new verification email from your profile page.
                </p>
              </>
            )}

            {!verifying && (
              <div className="flex flex-col gap-2">
                <Button
                  onClick={goToDashboard}
                  className="w-full"
                  variant={verified ? "default" : "outline"}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Button>
                
                {error && (
                  <Button
                    onClick={verifyEmail}
                    variant="outline"
                    className="w-full"
                  >
                    Try Again
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold">Loading...</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground">
              Please wait while we load the verification page...
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <VerifyEmailContent />
    </Suspense>
  );
}
